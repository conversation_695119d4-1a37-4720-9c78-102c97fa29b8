document.addEventListener('DOMContentLoaded', function() {
    // Helper function to get CSRF token
    function getCSRFToken() {
        const token = document.querySelector('input[name="csrf_token"]');
        return token ? token.value : '';
    }

    // Initialize attendance summary chart
    const ctx = document.getElementById('attendanceSummaryChart')?.getContext('2d');
    if (ctx) {
        const presentDays = parseInt(document.querySelector('.bg-success h4').textContent) || 0;
        const absentDays = parseInt(document.querySelector('.bg-danger h4').textContent) || 0;
        const lateDays = parseInt(document.querySelector('.bg-warning h4').textContent) || 0;
        const leaveDays = parseInt(document.querySelector('.bg-info h4').textContent) || 0;

        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Present', 'Absent', 'Late', 'Leave'],
                datasets: [{
                    data: [presentDays, absentDays, lateDays, leaveDays],
                    backgroundColor: [
                        '#198754',
                        '#dc3545',
                        '#ffc107',
                        '#0dcaf0'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    // Initialize attendance calendar
    const calendarEl = document.getElementById('attendanceCalendar');
    if (calendarEl) {
        const calendar = new FullCalendar.Calendar(calendarEl, {
            initialView: 'dayGridMonth',
            headerToolbar: {
                left: 'prev,next today',
                center: 'title',
                right: 'dayGridMonth,timeGridWeek'
            },
            events: function (fetchInfo, successCallback, failureCallback) {
                fetch(`/staff/attendance_calendar?start=${fetchInfo.startStr}&end=${fetchInfo.endStr}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            const events = [];

                            // Add attendance events
                            data.attendance.forEach(record => {
                                let color, title;
                                const shiftType = record.shift_type ? record.shift_type.charAt(0).toUpperCase() + record.shift_type.slice(1) : 'General';

                                // Format duration text for display
                                let durationText = '';
                                if (record.late_duration_minutes > 0) {
                                    const hours = Math.floor(record.late_duration_minutes / 60);
                                    const mins = record.late_duration_minutes % 60;
                                    if (hours > 0) {
                                        durationText = ` (Late by ${hours}h ${mins}m)`;
                                    } else {
                                        durationText = ` (Late by ${mins}m)`;
                                    }
                                } else if (record.early_departure_minutes > 0) {
                                    const hours = Math.floor(record.early_departure_minutes / 60);
                                    const mins = record.early_departure_minutes % 60;
                                    if (hours > 0) {
                                        durationText = ` (Left ${hours}h ${mins}m early)`;
                                    } else {
                                        durationText = ` (Left ${mins}m early)`;
                                    }
                                }

                                if (record.status === 'present') {
                                    color = '#198754';
                                    title = `Present (${shiftType}) - ${record.time_in || 'N/A'}`;
                                } else if (record.status === 'absent') {
                                    color = '#dc3545';
                                    title = `Absent (${shiftType})`;
                                } else if (record.status === 'late') {
                                    color = '#ffc107';
                                    title = `Late (${shiftType}) - ${record.time_in || 'N/A'}${durationText}`;
                                } else if (record.status === 'left_soon') {
                                    color = '#fd7e14';
                                    title = `Left Soon (${shiftType}) - Out: ${record.time_out || 'N/A'}${durationText}`;
                                } else if (record.status === 'leave') {
                                    color = '#0dcaf0';
                                    title = `On Leave (${shiftType})`;
                                }

                                events.push({
                                    title: title,
                                    start: record.date,
                                    allDay: true,
                                    backgroundColor: color,
                                    borderColor: color,
                                    className: `attendance-${record.status}`,
                                    extendedProps: {
                                        type: 'attendance',
                                        timeIn: record.time_in,
                                        timeOut: record.time_out,
                                        overtimeIn: record.overtime_in,
                                        overtimeOut: record.overtime_out,
                                        status: record.status,
                                        notes: record.notes,
                                        shiftType: shiftType,
                                        lateDurationMinutes: record.late_duration_minutes,
                                        earlyDepartureMinutes: record.early_departure_minutes,
                                        shiftStartTime: record.shift_start_time,
                                        shiftEndTime: record.shift_end_time,
                                        regularizationRequested: record.regularization_requested,
                                        regularizationStatus: record.regularization_status
                                    }
                                });
                            });

                            // Add leave events
                            data.leaves.forEach(leave => {
                                const startDate = new Date(leave.start_date);
                                const endDate = new Date(leave.end_date);
                                endDate.setDate(endDate.getDate() + 1); // FullCalendar end date is exclusive

                                events.push({
                                    title: `${leave.leave_type} Leave`,
                                    start: leave.start_date,
                                    end: leave.end_date,
                                    allDay: true,
                                    backgroundColor: '#6f42c1',
                                    borderColor: '#6f42c1',
                                    className: 'attendance-leave',
                                    extendedProps: {
                                        type: 'leave',
                                        leaveType: leave.leave_type,
                                        status: leave.status
                                    }
                                });
                            });

                            successCallback(events);
                        } else {
                            failureCallback(new Error(data.error || 'Failed to load calendar data'));
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching calendar data:', error);
                        failureCallback(error);
                    });
            },
            eventClick: function (info) {
                const props = info.event.extendedProps;
                let content = `<strong>${info.event.title}</strong><br>`;
                content += `<strong>Date:</strong> ${info.event.start.toLocaleDateString()}<br>`;

                if (props.type === 'attendance') {
                    if (props.shiftType) content += `<strong>Shift Type:</strong> ${props.shiftType}<br>`;
                    if (props.timeIn) content += `<strong>Check-in:</strong> ${props.timeIn}<br>`;
                    if (props.timeOut) content += `<strong>Check-out:</strong> ${props.timeOut}<br>`;
                    if (props.overtimeIn) content += `<strong>Overtime In:</strong> ${props.overtimeIn}<br>`;
                    if (props.overtimeOut) content += `<strong>Overtime Out:</strong> ${props.overtimeOut}<br>`;
                    if (props.notes) content += `<strong>Notes:</strong> ${props.notes}<br>`;
                } else if (props.type === 'leave') {
                    content += `<strong>Leave Type:</strong> ${props.leaveType}<br>`;
                    content += `<strong>Status:</strong> ${props.status}<br>`;
                }

                // Show in a simple alert for now (could be enhanced with a modal)
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = content;
                alert(tempDiv.textContent || tempDiv.innerText);
            }
        });
        calendar.render();
    }

    // Edit Profile functionality
    document.getElementById('saveProfileBtn')?.addEventListener('click', function() {
        const form = document.getElementById('editProfileForm');
        const formData = new FormData(form);

        fetch('/staff/update_profile', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Profile updated successfully!');
                bootstrap.Modal.getInstance(document.getElementById('editProfileModal')).hide();
                location.reload(); // Reload to show updated information
            } else {
                alert(data.error || 'Failed to update profile');
            }
        })
        .catch(error => {
            console.error('Error updating profile:', error);
            alert('Error updating profile');
        });
    });

    // Change Password functionality
    document.getElementById('changePasswordBtn')?.addEventListener('click', function() {
        const form = document.getElementById('changePasswordForm');
        const formData = new FormData(form);

        // Validate passwords match
        const newPassword = formData.get('new_password');
        const confirmPassword = formData.get('confirm_password');

        if (newPassword !== confirmPassword) {
            alert('New passwords do not match');
            return;
        }

        fetch('/staff/change_password', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Password changed successfully!');
                bootstrap.Modal.getInstance(document.getElementById('changePasswordModal')).hide();
                form.reset();
            } else {
                alert(data.error || 'Failed to change password');
            }
        })
        .catch(error => {
            console.error('Error changing password:', error);
            alert('Error changing password');
        });
    });

    // Apply Leave functionality
    document.getElementById('submitLeave')?.addEventListener('click', function() {
        const form = document.getElementById('leaveForm');
        const formData = new FormData(form);

        // Basic validation
        const leaveType = formData.get('leave_type');
        const startDate = formData.get('start_date');
        const endDate = formData.get('end_date');
        const reason = formData.get('reason');

        if (!leaveType || !startDate || !endDate || !reason) {
            alert('Please fill all fields');
            return;
        }

        // Check if end date is after start date
        if (new Date(endDate) < new Date(startDate)) {
            alert('End date must be after start date');
            return;
        }

        fetch('/apply_leave', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Leave application submitted successfully!');
                bootstrap.Modal.getInstance(document.getElementById('applyLeaveModal')).hide();
                form.reset();
                location.reload(); // Reload to show new leave application
            } else {
                alert(data.error || 'Failed to submit leave application');
            }
        })
        .catch(error => {
            console.error('Error submitting leave:', error);
            alert('Error submitting leave application');
        });
    });

    // Set minimum date for leave application to today
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('startDate')?.setAttribute('min', today);
    document.getElementById('endDate')?.setAttribute('min', today);

    // Update end date minimum when start date changes
    document.getElementById('startDate')?.addEventListener('change', function() {
        const endDateInput = document.getElementById('endDate');
        if (endDateInput) {
            endDateInput.setAttribute('min', this.value);
            if (endDateInput.value && endDateInput.value < this.value) {
                endDateInput.value = this.value;
            }
        }
    });
});
