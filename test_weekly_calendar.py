#!/usr/bin/env python3
"""
Weekly Calendar Test Suite

This script tests the new weekly calendar functionality:
1. Backend API endpoint (/get_weekly_attendance)
2. Weekly data calculation and formatting
3. Delay and early departure calculations
4. Frontend integration
"""

import requests
import json
import sys
from datetime import datetime, date, timedelta
import sqlite3


class WeeklyCalendarTestSuite:
    """Test suite for weekly calendar functionality"""
    
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
    
    def log_test(self, test_name, passed, message=""):
        """Log test result"""
        status = "✅ PASS" if passed else "❌ FAIL"
        self.test_results.append({
            'name': test_name,
            'passed': passed,
            'message': message
        })
        print(f"{status}: {test_name}")
        if message:
            print(f"    {message}")
    
    def login_as_staff(self):
        """Login as staff for testing"""
        try:
            # Get login page first
            response = self.session.get(f"{self.base_url}/")
            if response.status_code != 200:
                return False
            
            # Try to login as staff
            login_data = {
                'school_id': '1',
                'username': 'STAFF001',
                'password': 'staff123'
            }
            
            response = self.session.post(f"{self.base_url}/login", data=login_data)
            return response.status_code == 200 and 'staff_dashboard' in response.text
            
        except Exception as e:
            print(f"Staff login failed: {e}")
            return False
    
    def login_as_admin(self):
        """Login as admin for testing"""
        try:
            # Logout first
            self.session.get(f"{self.base_url}/logout")
            
            # Try to login as admin
            login_data = {
                'school_id': '1',
                'username': 'admin',
                'password': 'admin123'
            }
            
            response = self.session.post(f"{self.base_url}/login", data=login_data)
            return response.status_code == 200 and 'admin_dashboard' in response.text
            
        except Exception as e:
            print(f"Admin login failed: {e}")
            return False
    
    def test_server_availability(self):
        """Test if server is running"""
        print("\n=== Testing Server Availability ===")
        
        try:
            response = self.session.get(f"{self.base_url}/")
            if response.status_code == 200:
                self.log_test("Server availability", True)
                return True
            else:
                self.log_test("Server availability", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_test("Server availability", False, str(e))
            return False
    
    def test_weekly_attendance_endpoint(self):
        """Test the /get_weekly_attendance endpoint"""
        print("\n=== Testing Weekly Attendance Endpoint ===")
        
        if not self.login_as_staff():
            self.log_test("Staff login for weekly calendar tests", False, "Could not login as staff")
            return
        
        # Test with current week
        try:
            today = date.today()
            # Get Monday of current week
            monday = today - timedelta(days=today.weekday())
            week_start = monday.strftime('%Y-%m-%d')
            
            response = self.session.get(f"{self.base_url}/get_weekly_attendance?week_start={week_start}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.log_test("Weekly attendance endpoint - success response", True)
                    
                    # Check data structure
                    required_fields = ['staff_info', 'week_start', 'week_end', 'weekly_data']
                    missing_fields = [field for field in required_fields if field not in data]
                    
                    if not missing_fields:
                        self.log_test("Weekly attendance endpoint - data structure", True)
                    else:
                        self.log_test("Weekly attendance endpoint - data structure", False, f"Missing fields: {missing_fields}")
                    
                    # Check weekly data structure
                    weekly_data = data.get('weekly_data', [])
                    if len(weekly_data) == 7:
                        self.log_test("Weekly attendance endpoint - 7 days data", True)
                        
                        # Check first day structure
                        if weekly_data:
                            first_day = weekly_data[0]
                            day_fields = ['day_name', 'date', 'present_status', 'shift_type_display']
                            missing_day_fields = [field for field in day_fields if field not in first_day]
                            
                            if not missing_day_fields:
                                self.log_test("Weekly attendance endpoint - day data structure", True)
                            else:
                                self.log_test("Weekly attendance endpoint - day data structure", False, f"Missing day fields: {missing_day_fields}")
                    else:
                        self.log_test("Weekly attendance endpoint - 7 days data", False, f"Got {len(weekly_data)} days instead of 7")
                        
                else:
                    self.log_test("Weekly attendance endpoint - success response", False, data.get('error', 'Unknown error'))
            else:
                self.log_test("Weekly attendance endpoint - HTTP status", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            self.log_test("Weekly attendance endpoint", False, str(e))
    
    def test_admin_weekly_attendance_endpoint(self):
        """Test the /get_weekly_attendance endpoint as admin"""
        print("\n=== Testing Admin Weekly Attendance Endpoint ===")
        
        if not self.login_as_admin():
            self.log_test("Admin login for weekly calendar tests", False, "Could not login as admin")
            return
        
        # Test with staff_id parameter
        try:
            today = date.today()
            monday = today - timedelta(days=today.weekday())
            week_start = monday.strftime('%Y-%m-%d')
            
            # Try to get data for staff ID 1
            response = self.session.get(f"{self.base_url}/get_weekly_attendance?week_start={week_start}&staff_id=1")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.log_test("Admin weekly attendance endpoint - success", True)
                    
                    # Check if staff_info is included
                    staff_info = data.get('staff_info', {})
                    if 'full_name' in staff_info and 'staff_id' in staff_info:
                        self.log_test("Admin weekly attendance endpoint - staff info", True)
                    else:
                        self.log_test("Admin weekly attendance endpoint - staff info", False, "Missing staff information")
                        
                else:
                    self.log_test("Admin weekly attendance endpoint - success", False, data.get('error', 'Unknown error'))
            else:
                self.log_test("Admin weekly attendance endpoint - HTTP status", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            self.log_test("Admin weekly attendance endpoint", False, str(e))
    
    def test_database_integration(self):
        """Test database integration for weekly calendar"""
        print("\n=== Testing Database Integration ===")
        
        try:
            # Connect to database
            conn = sqlite3.connect('vishnorex.db')
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # Test shift_definitions table
            cursor.execute("SELECT COUNT(*) as count FROM shift_definitions WHERE is_active = 1")
            shift_count = cursor.fetchone()['count']
            
            if shift_count > 0:
                self.log_test("Database - shift definitions exist", True, f"Found {shift_count} active shifts")
            else:
                self.log_test("Database - shift definitions exist", False, "No active shift definitions found")
            
            # Test attendance table with enhanced columns
            cursor.execute("PRAGMA table_info(attendance)")
            columns = [col[1] for col in cursor.fetchall()]
            
            required_columns = ['late_duration_minutes', 'early_departure_minutes']
            missing_columns = [col for col in required_columns if col not in columns]
            
            if not missing_columns:
                self.log_test("Database - enhanced attendance columns", True)
            else:
                self.log_test("Database - enhanced attendance columns", False, f"Missing columns: {missing_columns}")
            
            # Test staff table with shift_type
            cursor.execute("PRAGMA table_info(staff)")
            staff_columns = [col[1] for col in cursor.fetchall()]
            
            if 'shift_type' in staff_columns:
                self.log_test("Database - staff shift_type column", True)
            else:
                self.log_test("Database - staff shift_type column", False, "shift_type column missing from staff table")
            
            conn.close()
            
        except Exception as e:
            self.log_test("Database integration", False, str(e))
    
    def test_date_calculations(self):
        """Test date calculation functions"""
        print("\n=== Testing Date Calculations ===")
        
        try:
            # Test week start calculation (should be Monday)
            test_date = date(2024, 1, 10)  # Wednesday
            expected_monday = date(2024, 1, 8)  # Monday of that week
            
            # Simulate the getWeekStart logic from JavaScript
            day = test_date.weekday()  # 0 = Monday, 6 = Sunday
            diff = -day  # Days to subtract to get to Monday
            calculated_monday = test_date + timedelta(days=diff)
            
            if calculated_monday == expected_monday:
                self.log_test("Date calculations - week start", True)
            else:
                self.log_test("Date calculations - week start", False, f"Expected {expected_monday}, got {calculated_monday}")
            
            # Test week end calculation
            expected_sunday = expected_monday + timedelta(days=6)
            calculated_sunday = calculated_monday + timedelta(days=6)
            
            if calculated_sunday == expected_sunday:
                self.log_test("Date calculations - week end", True)
            else:
                self.log_test("Date calculations - week end", False, f"Expected {expected_sunday}, got {calculated_sunday}")
                
        except Exception as e:
            self.log_test("Date calculations", False, str(e))
    
    def run_all_tests(self):
        """Run all tests"""
        print("🚀 Starting Weekly Calendar Test Suite")
        print("=" * 60)
        
        # Check server availability first
        if not self.test_server_availability():
            print("\n❌ Server is not available. Please start the Flask application first.")
            return False
        
        self.test_database_integration()
        self.test_date_calculations()
        self.test_weekly_attendance_endpoint()
        self.test_admin_weekly_attendance_endpoint()
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 WEEKLY CALENDAR TEST SUMMARY")
        print("=" * 60)
        
        passed_tests = sum(1 for result in self.test_results if result['passed'])
        total_tests = len(self.test_results)
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {total_tests - passed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        # Show failed tests
        failed_tests = [result for result in self.test_results if not result['passed']]
        if failed_tests:
            print("\n❌ FAILED TESTS:")
            for test in failed_tests:
                print(f"  - {test['name']}: {test['message']}")
        
        return passed_tests == total_tests


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Test weekly calendar functionality')
    parser.add_argument('--url', default='http://localhost:5000', help='Base URL of the Flask application')
    args = parser.parse_args()
    
    test_suite = WeeklyCalendarTestSuite(args.url)
    success = test_suite.run_all_tests()
    
    if success:
        print("\n🎉 All weekly calendar tests passed!")
        sys.exit(0)
    else:
        print("\n⚠️  Some tests failed. Please check the implementation.")
        sys.exit(1)
