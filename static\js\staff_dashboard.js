document.addEventListener('DOMContentLoaded', function() {
    // Helper function to get CSRF token
    function getCSRFToken() {
        const token = document.querySelector('input[name="csrf_token"]');
        return token ? token.value : '';
    }
    // Initialize attendance chart with real data
    const ctx = document.getElementById('attendanceChart')?.getContext('2d');
    if (ctx) {
        fetch('/get_attendance_summary')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const attendanceChart = new Chart(ctx, {
                        type: 'doughnut',
                        data: {
                            labels: ['Present', 'Absent', 'Late', 'Leave'],
                            datasets: [{
                                data: [data.present, data.absent, data.late, data.leave],
                                backgroundColor: [
                                    '#198754',
                                    '#dc3545',
                                    '#ffc107',
                                    '#0dcaf0'
                                ],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            plugins: {
                                legend: {
                                    position: 'bottom'
                                }
                            }
                        }
                    });

                    // Update counts
                    document.getElementById('presentDays').textContent = data.present;
                    document.getElementById('absentDays').textContent = data.absent;
                    document.getElementById('lateDays').textContent = data.late;
                    document.getElementById('leaveDays').textContent = data.leave;
                }
            });
    }

    // Biometric authentication
    const startAuthBtn = document.getElementById('startAuthBtn');
    const fingerprintScanner = document.getElementById('fingerprintScanner');
    const faceScanner = document.getElementById('faceScanner');
    const authStatus = document.getElementById('authStatus');
    const attendanceStatus = document.getElementById('attendanceStatus');
    const markInBtn = document.getElementById('markInBtn');
    const markOutBtn = document.getElementById('markOutBtn');

    let authStep = 0; // 0: not started, 1: fingerprint, 2: face

    // Removed duplicate event listener - using the one at line 190

    function fingerprintScan() {
        authStatus.innerHTML = '<div class="alert alert-info">Scanning fingerprint... Please place your finger on the scanner</div>';

        setTimeout(() => {
            authStatus.innerHTML = '<div class="alert alert-success">Fingerprint verified successfully</div>';
            fingerprintScanner.style.display = 'none';
            faceScanner.style.display = 'block';
            authStep = 2;
            faceRecognition();
        }, 2000);
    }

    function faceRecognition() {
        authStatus.innerHTML = '<div class="alert alert-info">Starting face recognition... Please look at the camera</div>';

        if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
            navigator.mediaDevices.getUserMedia({ video: true })
                .then(function(stream) {
                    const video = document.getElementById('faceVideo');
                    video.srcObject = stream;

                    setTimeout(() => {
                        const canvas = document.getElementById('canvasElement');
                        const context = canvas.getContext('2d');
                        context.drawImage(video, 0, 0, canvas.width, canvas.height);

                        stream.getTracks().forEach(track => track.stop());
                        video.srcObject = null;

                        completeAuthentication();
                    }, 3000);
                });
        }
    }

    function completeAuthentication() {
        authStatus.innerHTML = '<div class="alert alert-success">Biometric authentication successful!</div>';
        faceScanner.style.display = 'none';
        attendanceStatus.innerHTML = '<h4>Status: <span class="text-success">Ready to Mark Attendance</span></h4>';
        markInBtn.disabled = false;
    }

    // Initialize calendar with enhanced attendance display
    const calendarEl = document.getElementById('calendar');
    if (calendarEl) {
        const calendar = new FullCalendar.Calendar(calendarEl, {
            initialView: 'dayGridMonth',
            headerToolbar: {
                left: 'prev,next today',
                center: 'title',
                right: 'dayGridMonth,timeGridWeek'
            },
            height: 'auto',
            events: function (fetchInfo, successCallback, failureCallback) {
                fetch(`/get_staff_attendance?start=${fetchInfo.startStr}&end=${fetchInfo.endStr}`)
                    .then(response => response.json())
                    .then(data => {
                        const events = [];

                        // Process attendance records
                        data.attendance.forEach(record => {
                            let color, title, textColor = '#fff';
                            const shiftType = record.shift_type ? record.shift_type.charAt(0).toUpperCase() + record.shift_type.slice(1) : 'General';

                            // Format duration text for display
                            let durationText = '';
                            if (record.late_duration_minutes > 0) {
                                const hours = Math.floor(record.late_duration_minutes / 60);
                                const mins = record.late_duration_minutes % 60;
                                if (hours > 0) {
                                    durationText = ` (Late by ${hours}h ${mins}m)`;
                                } else {
                                    durationText = ` (Late by ${mins}m)`;
                                }
                            } else if (record.early_departure_minutes > 0) {
                                const hours = Math.floor(record.early_departure_minutes / 60);
                                const mins = record.early_departure_minutes % 60;
                                if (hours > 0) {
                                    durationText = ` (Left ${hours}h ${mins}m early)`;
                                } else {
                                    durationText = ` (Left ${mins}m early)`;
                                }
                            }

                            if (record.status === 'present') {
                                color = '#198754';
                                title = `✓ Present (${shiftType})`;
                                if (record.time_in) title += ` - ${record.time_in}`;
                            } else if (record.status === 'absent') {
                                color = '#dc3545';
                                title = `✗ Absent (${shiftType})`;
                            } else if (record.status === 'late') {
                                color = '#ffc107';
                                title = `⚠ Late (${shiftType})`;
                                textColor = '#000';
                                if (record.time_in) title += ` - ${record.time_in}${durationText}`;
                            } else if (record.status === 'left_soon') {
                                color = '#fd7e14';
                                title = `⏰ Left Soon (${shiftType})`;
                                textColor = '#000';
                                if (record.time_out) title += ` - Out: ${record.time_out}${durationText}`;
                            } else if (record.status === 'leave') {
                                color = '#0dcaf0';
                                title = `📅 Leave (${shiftType})`;
                                textColor = '#000';
                            }

                            events.push({
                                title: title,
                                start: record.date,
                                allDay: true,
                                backgroundColor: color,
                                borderColor: color,
                                textColor: textColor,
                                className: `attendance-${record.status}`,
                                extendedProps: {
                                    type: 'attendance',
                                    timeIn: record.time_in,
                                    timeOut: record.time_out,
                                    overtimeIn: record.overtime_in,
                                    overtimeOut: record.overtime_out,
                                    status: record.status,
                                    shiftType: shiftType,
                                    lateDurationMinutes: record.late_duration_minutes,
                                    earlyDepartureMinutes: record.early_departure_minutes,
                                    shiftStartTime: record.shift_start_time,
                                    shiftEndTime: record.shift_end_time,
                                    regularizationRequested: record.regularization_requested,
                                    regularizationStatus: record.regularization_status
                                }
                            });
                        });

                        // Add holidays if available
                        if (data.holidays) {
                            data.holidays.forEach(holiday => {
                                events.push({
                                    title: `🏛️ ${holiday.name}`,
                                    start: holiday.date,
                                    allDay: true,
                                    backgroundColor: '#6c757d',
                                    borderColor: '#6c757d',
                                    textColor: '#fff',
                                    className: 'holiday',
                                    extendedProps: {
                                        type: 'holiday'
                                    }
                                });
                            });
                        }

                        successCallback(events);
                    })
                    .catch(error => {
                        console.error('Error fetching attendance:', error);
                        failureCallback(error);
                    });
            },
            eventClick: function (info) {
                const props = info.event.extendedProps;
                let message = `Date: ${info.event.start.toLocaleDateString()}\n`;
                message += `Status: ${props.status ? props.status.charAt(0).toUpperCase() + props.status.slice(1) : 'Unknown'}\n`;

                if (props.type === 'attendance') {
                    if (props.shiftType) message += `Shift Type: ${props.shiftType}\n`;
                    if (props.shiftStartTime && props.shiftEndTime) {
                        message += `Shift Hours: ${props.shiftStartTime} - ${props.shiftEndTime}\n`;
                    }
                    if (props.timeIn) message += `Check-in: ${props.timeIn}\n`;
                    if (props.timeOut) message += `Check-out: ${props.timeOut}\n`;
                    if (props.overtimeIn) message += `Overtime In: ${props.overtimeIn}\n`;
                    if (props.overtimeOut) message += `Overtime Out: ${props.overtimeOut}\n`;

                    // Show duration information
                    if (props.lateDurationMinutes > 0) {
                        const hours = Math.floor(props.lateDurationMinutes / 60);
                        const mins = props.lateDurationMinutes % 60;
                        if (hours > 0) {
                            message += `Late Duration: ${hours}h ${mins}m\n`;
                        } else {
                            message += `Late Duration: ${mins}m\n`;
                        }
                    }
                    if (props.earlyDepartureMinutes > 0) {
                        const hours = Math.floor(props.earlyDepartureMinutes / 60);
                        const mins = props.earlyDepartureMinutes % 60;
                        if (hours > 0) {
                            message += `Early Departure: ${hours}h ${mins}m\n`;
                        } else {
                            message += `Early Departure: ${mins}m\n`;
                        }
                    }

                    // Show regularization status
                    if (props.regularizationRequested) {
                        message += `Regularization: ${props.regularizationStatus || 'Pending'}\n`;
                    }
                }

                alert(message);
            },
            dayCellDidMount: function(info) {
                // Add custom styling for weekends
                if (info.date.getDay() === 0 || info.date.getDay() === 6) {
                    info.el.style.backgroundColor = '#f8f9fa';
                }
            }
        });
        calendar.render();
    }

    // Global variables for biometric verification
    let isVerificationInProgress = false;

    // Load today's attendance status on page load
    loadTodayAttendanceStatus();

    // Start automatic polling for device verifications
    startDevicePolling();

    // Check device connection status
    checkDeviceStatus();

    function loadTodayAttendanceStatus() {
        fetch('/get_today_attendance_status')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateAttendanceDisplay(data.attendance);
                    updateVerificationHistory(data.verifications);
                    updateAvailableActions(data.available_actions);
                }
            })
            .catch(error => {
                console.error('Error loading attendance status:', error);
            });
    }

    function updateAttendanceDisplay(attendance) {
        const currentStatus = document.getElementById('currentStatus');
        const timeIn = document.getElementById('timeIn');
        const timeOut = document.getElementById('timeOut');
        const overtimeIn = document.getElementById('overtimeIn');
        const overtimeOut = document.getElementById('overtimeOut');

        // Update today's status widget
        const todayStatusText = document.getElementById('todayStatusText');
        const todayCheckIn = document.getElementById('todayCheckIn');
        const todayCheckOut = document.getElementById('todayCheckOut');

        if (attendance) {
            timeIn.textContent = attendance.time_in || '--:--:--';
            timeOut.textContent = attendance.time_out || '--:--:--';
            overtimeIn.textContent = attendance.overtime_in || '--:--:--';
            overtimeOut.textContent = attendance.overtime_out || '--:--:--';

            // Update today's status widget
            todayCheckIn.textContent = attendance.time_in || '--:--';
            todayCheckOut.textContent = attendance.time_out || '--:--';

            // Update status based on attendance
            let statusText, statusClass, todayStatusClass;
            if (attendance.overtime_out) {
                statusText = 'All Complete';
                statusClass = 'text-success';
                todayStatusClass = 'text-success';
                todayStatusText.textContent = '✅ Day Complete';
            } else if (attendance.overtime_in) {
                statusText = 'Overtime In Progress';
                statusClass = 'text-warning';
                todayStatusClass = 'text-warning';
                todayStatusText.textContent = '⏰ Overtime Active';
            } else if (attendance.time_out) {
                statusText = 'Regular Hours Complete';
                statusClass = 'text-info';
                todayStatusClass = 'text-info';
                todayStatusText.textContent = '✓ Regular Hours Done';
            } else if (attendance.time_in) {
                statusText = 'Checked In';
                statusClass = 'text-primary';
                todayStatusClass = 'text-primary';
                todayStatusText.textContent = '🟢 Checked In';
            } else {
                statusText = 'Not Marked';
                statusClass = 'text-secondary';
                todayStatusClass = 'text-secondary';
                todayStatusText.textContent = '⚪ Not Marked';
            }

            currentStatus.textContent = statusText;
            currentStatus.className = statusClass;
            todayStatusText.className = todayStatusClass;
        } else {
            currentStatus.textContent = 'Not Marked';
            currentStatus.className = 'text-secondary';
            timeIn.textContent = '--:--:--';
            timeOut.textContent = '--:--:--';
            overtimeIn.textContent = '--:--:--';
            overtimeOut.textContent = '--:--:--';

            // Update today's status widget
            todayStatusText.textContent = '⚪ Not Marked';
            todayStatusText.className = 'text-secondary';
            todayCheckIn.textContent = '--:--';
            todayCheckOut.textContent = '--:--';
        }
    }

    function updateVerificationHistory(verifications) {
        const historyBody = document.getElementById('verificationHistory');

        if (verifications.length === 0) {
            historyBody.innerHTML = '<tr><td colspan="4" class="text-center">No verifications today</td></tr>';
            return;
        }

        historyBody.innerHTML = verifications.map(v => {
            const time = new Date(v.verification_time).toLocaleTimeString();
            const statusBadge = v.verification_status === 'success' ?
                '<span class="badge bg-success">Success</span>' :
                '<span class="badge bg-danger">Failed</span>';

            return `
                <tr>
                    <td>${time}</td>
                    <td>${v.verification_type}</td>
                    <td>${v.biometric_method}</td>
                    <td>${statusBadge}</td>
                </tr>
            `;
        }).join('');
    }

    function updateAvailableActions(availableActions) {
        // This function is now simplified since we don't have manual selection
        // Just update the device status display
        const deviceStatus = document.getElementById('deviceStatus');

        if (availableActions.length === 0) {
            deviceStatus.innerHTML = '<span class="badge bg-success">All Complete</span>';
        } else {
            deviceStatus.innerHTML = '<span class="badge bg-primary">Ready for Verification</span>';
        }
    }

    function startDevicePolling() {
        // Poll for new device verifications every 30 seconds
        setInterval(() => {
            pollForDeviceVerifications();
        }, 30000);

        // Also poll immediately
        pollForDeviceVerifications();
    }

    function pollForDeviceVerifications() {
        fetch('/get_latest_device_verifications?since_minutes=1')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.verifications.length > 0) {
                    // Check if any verification is for the current user
                    const currentUserId = getCurrentUserId(); // We'll need to implement this
                    const userVerifications = data.verifications.filter(v => v.user_id === currentUserId);

                    if (userVerifications.length > 0) {
                        // Show the latest verification
                        const latest = userVerifications[0];
                        showVerificationSuccess(latest);

                        // Reload attendance status
                        setTimeout(() => {
                            loadTodayAttendanceStatus();
                        }, 1000);
                    }
                }
            })
            .catch(error => {
                console.error('Error polling for device verifications:', error);
            });
    }

    function showVerificationSuccess(verification) {
        const authStatus = document.getElementById('authStatus');
        authStatus.innerHTML = `
            <div class="alert alert-success">
                <i class="bi bi-check-circle"></i> ${verification.verification_type.replace('-', ' ').toUpperCase()} recorded successfully!<br>
                <small><strong>Time:</strong> ${verification.time_only}</small><br>
                <small><strong>Verified:</strong> ${verification.timestamp}</small>
            </div>
        `;

        // Clear the message after 5 seconds
        setTimeout(() => {
            authStatus.innerHTML = '';
        }, 5000);
    }

    function getCurrentUserId() {
        // Extract user ID from session or page data
        // For now, we'll try to get it from the page context
        // This should be set when the page loads
        return window.currentStaffId || null;
    }

    function checkDeviceStatus() {
        fetch('/test_biometric_connection', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `device_ip=*************&csrf_token=${encodeURIComponent(getCSRFToken())}`
        })
        .then(response => response.json())
        .then(data => {
            const deviceStatus = document.getElementById('deviceStatus');
            if (data.success) {
                deviceStatus.innerHTML = '<span class="badge bg-success">Connected</span>';
            } else {
                deviceStatus.innerHTML = '<span class="badge bg-danger">Disconnected</span>';
            }
        })
        .catch(error => {
            const deviceStatus = document.getElementById('deviceStatus');
            deviceStatus.innerHTML = '<span class="badge bg-warning">Unknown</span>';
        });
    }

    // Apply leave
    const submitLeave = document.getElementById('submitLeave');
    submitLeave?.addEventListener('click', function () {
        const leaveType = document.getElementById('leaveType').value;
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;
        const reason = document.getElementById('leaveReason').value;

        if (!leaveType || !startDate || !endDate || !reason) {
            alert('Please fill all fields');
            return;
        }

        fetch('/apply_leave', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `leave_type=${leaveType}&start_date=${startDate}&end_date=${endDate}&reason=${encodeURIComponent(reason)}&csrf_token=${encodeURIComponent(getCSRFToken())}`
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Leave application submitted successfully');
                    bootstrap.Modal.getInstance(document.getElementById('applyLeaveModal')).hide();
                    location.reload();
                } else {
                    alert(data.error || 'Failed to submit leave application');
                }
            });
    });

    // Updated download report with date selection
// Updated download report with date selection
document.getElementById('downloadReportBtn')?.addEventListener('click', function() {
    const startDate = prompt('Enter start date (YYYY-MM-DD):');
    if (!startDate) return;

    const endDate = prompt('Enter end date (YYYY-MM-DD):');
    if (!endDate) return;

    fetch(`/export_staff_report?start_date=${startDate}&end_date=${endDate}`)
        .then(response => response.blob())
        .then(blob => {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `attendance_report_${startDate}_to_${endDate}.csv`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        });
});
});
